 <!-- Temmplate Basics -->
 <div class="col-xl-3 mt-2">
    <div class="card shadow">
        <div class="card-header bg-white border-0">
            <div class="row align-items-center">
                <div class="col-8">
                    <h3 class="mb-0"><?php echo e(__('Template basics')); ?></h3>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- Template Name -->
            <div class="form-group">
                <label for="name"><?php echo e(__('Name')); ?></label>
                <input v-model="template_name" type="text" name="name" id="name" class="form-control" placeholder="<?php echo e(__('Name')); ?>" required>
            </div>

            <!-- Template Category - Marketing,Utility -->
            <div class="form-group">
                <label for="category"><?php echo e(__('Category')); ?></label>
                <select v-model="category" name="category" id="category" class="form-control">
                    <option value="MARKETING"><?php echo e(__('Marketing')); ?></option>
                    <option value="UTILITY"><?php echo e(__('Utility')); ?></option>
                </select>
            </div>

            <!-- Template Language -->
            <div class="form-group">
                <label for="language"><?php echo e(__('Language')); ?></label>
                <select v-model="language" name="language" id="language" class="form-control">
                    <option value=""><?php echo e(__('Select language')); ?></option>
                    <?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                     <option value="<?php echo e($language[1]); ?>"><?php echo e($language[0]); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                   
                  
                    
                </select>
            </div>
        </div>

    </div>
</div><?php /**PATH C:\xampp\htdocs\zaptra\modules\Wpbox\Providers/../Resources/views/templates/partials/basic.blade.php ENDPATH**/ ?>