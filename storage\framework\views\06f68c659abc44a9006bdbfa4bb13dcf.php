  <!-- Temmplate Details -->
  <div class="col-xl-5 mt-2">
    <div class="card shadow">
        <div class="card-header bg-white border-0">
            <div class="row align-items-center">
                <div class="col-8">
                    <h3 class="mb-0"><?php echo e(__('Form')); ?></h3>
                </div>
            </div>
        </div>
        <div class="card-body">
           <!-- Template header - None, Text, Media -->
            <div class="form-group">
                <label for="header"><strong><?php echo e(__('Header type')); ?></strong></label>
                <span class="badge badge-primary" style="color:#8898aa"><?php echo e(__('Optional')); ?></span><br />
                <small><?php echo e(__('Add a title or choose which type of media you will use for this header.')); ?></small>
                <select name="header" id="header" class="form-control" v-model="headerType">
                    <option value="none"><?php echo e(__('None')); ?></option>
                    <option value="text"><?php echo e(__('Text')); ?></option>
                    <option value="image"><?php echo e(__('Image')); ?></option>
                    <option value="video"><?php echo e(__('Video')); ?></option>
                    <option value="pdf"><?php echo e(__('PDF')); ?></option>
                </select>
            </div>

            <!-- Templpate header text -->
            <div v-if="headerType=='text'" class="form-group">
                <label for="header_text"><strong><?php echo e(__('Header text')); ?></strong></label>
                <div class="input-group">
                    <input v-model="headerText" type="text" name="header_text" id="header_text" class="form-control" placeholder="<?php echo e(__('Header text')); ?>" value="<?php echo e(old('header_text')); ?>">
                    <div class="input-group-append">
                        <button type="button" class="btn btn-outline-primary btn-sm" @click="addHeaderVariable()"><?php echo e(__('Add variable')); ?></button>
                    </div>
                </div>

                <div class="mt-2">
                    <small><?php echo e(__('You can use variables to personalize the header text.')); ?></small>
                </div>

                <div class="mt-2" v-if="headervariableAdded">
                    <div class="form-group p-4 "  style="background-color: #e9ecef; !important">
                        <label for="headerExampleVariable"><strong><?php echo e(__('Samples for header content')); ?></strong></label>
                        <br /><small><?php echo e(__('To help us review your content, provide examples of the variables in the header.')); ?></small>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                
                                <span class="input-group-text" id="basic-addon1">{{ '{' }}{1}{{ '}' }}</span>
                            </div>
                            <input v-model="headerExampleVariable" type="text" class="form-control" placeholder="<?php echo e(__('Enter content for the heder variable')); ?>" aria-describedby="basic-addon1">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Template header image -->
            <div v-if="headerType=='image'" class="form-group">
                <label for="header_image"><strong><?php echo e(__('Header image')); ?></strong></label>
                <input @change="handleImageUpload" type="file"  accept="image/*" name="header_image" id="header_image" class="form-control" placeholder="<?php echo e(__('Header image')); ?>" value="<?php echo e(old('header_image')); ?>">
            </div>

            <!-- Template header video -->
            <div v-if="headerType=='video'" class="form-group">
                <label for="header_video"><strong><?php echo e(__('Header video')); ?></strong></label>
                <input @change="handleVideoUpload" type="file" accept="video/*" name="header_video" id="header_video" class="form-control" placeholder="<?php echo e(__('Header video')); ?>" value="<?php echo e(old('header_video')); ?>">
            </div>

            <!-- Template header pdf -->
            <div v-if="headerType=='pdf'" class="form-group">
                <label for="header_pdf"><strong><?php echo e(__('Header pdf')); ?></strong></label>
                <input @change="handlePdfUpload" type="file" accept="application/pdf" name="header_pdf" id="header_pdf" class="form-control" placeholder="<?php echo e(__('Header pdf')); ?>" value="<?php echo e(old('header_pdf')); ?>">
            </div>

            <hr />


            <!-- Body -->
            <div class="form-group">
                <label for="body"><strong><?php echo e(__('Body')); ?></strong></label>
                <span class="badge badge-primary" style="color:#8898aa"><?php echo e(__('Required')); ?></span>
                <p class="small"><?php echo e(__('Enter the text for your message in the language you have selected.')); ?></p>
                <textarea rows="5" v-model="bodyText" name="body" id="body" class="form-control" placeholder="<?php echo e(__('Body')); ?>" value="<?php echo e(old('body')); ?>"></textarea>
                <div class="text-right mt-4">
                    <button @click="addBold()" class="btn btn-outline-secondary btn-sm mx-2" type="button" title="Bold">
                        <strong>B</strong>
                    </button>
                    <button @click="addItalic()" class="btn btn-outline-secondary btn-sm mx-2" type="button" title="Italic">
                        <em>I</em>
                    </button>
                    <button @click="addCode()" class="btn btn-outline-secondary btn-sm mx-2" type="button" title="Code">
                        <code>&lt;&gt;</code>
                    </button>
                    <button @click="addVariable()" class="btn btn-secondary btn-sm mx-2" type="button">
                        <?php echo e(__('Add variable')); ?>

                    </button>
                </div>
                <div class="mt-2" v-if="bodyVariables">
                    <div class="form-group p-4 "  style="background-color: #e9ecef; !important">
                        <label for="headerExampleVariable"><strong><?php echo e(__('Samples for body content')); ?></strong></label>
                        <br /><small><?php echo e(__('To help us review your content, provide examples of the variables in the body.')); ?></small>
                        <div v-for="(v, index) in bodyVariables" class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text" id="basic-addon1">{{v}}</span>
                            </div>
                            <input v-model="bodyExampleVariable[index]" type="text" class="form-control" placeholder="<?php echo e(__('Enter content for the variable')); ?>" aria-describedby="basic-addon1">
                        </div>
                    </div>
                </div>
            </div>

            <hr />

            <!-- Footer -->
            <div class="form-group">
                <label for="footer"><strong><?php echo e(__('Footer')); ?></strong></label>
                <span class="badge badge-primary" style="color:#8898aa"><?php echo e(__('Optional')); ?></span>
                <p class="small"><?php echo e(__('Enter the text for your footer in the language you have selected.')); ?></p>
                <input v-model="footerText" type="text" name="footer" id="footer" class="form-control" placeholder="<?php echo e(__('Footer')); ?>" value="<?php echo e(old('footer')); ?>">
            </div>

            <hr />
            
            <!-- Quick Reply Buttonns -->
            <div class="form-group">
                <label for="footer"><strong><?php echo e(__('Quick Reply Buttons')); ?></strong></label>
                <span class="badge badge-primary" style="color:#8898aa"><?php echo e(__('Optional')); ?></span>
                <p class="small"><?php echo e(__('Create buttons that let customers respond to your message')); ?></p>
                
                <!-- Add the button -->
                <div class="text-right mt-2">
                    <button @click="addQuickReply()" class="btn btn-outline-primary btn-sm" type="button">
                        <span class="btn-inner--icon"><?php echo e(__('Add Quick Reply')); ?></span>
                    </button> 
                </div>
                <div class="mt-2" v-if="quickReplies.length>0">
                    <div class="form-group p-4 "  style="background-color: #e9ecef; !important">
                        <label><strong><?php echo e(__('Quick Reply buttons')); ?></strong></label>
                        <div v-for="(v, index) in quickReplies" class="form-group">
                            <div class="input-groups">
                                
                                <div class="row">
                                    <div class="col-10">
                                        <input v-model="quickReplies[index]" type="text" class="form-control mr-4 pr-4" placeholder="<?php echo e(__('Button text')); ?>">
                                    </div>
                                    <div class="col-2 mt-2">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" @click="deleteQuickReply(index)">
                                            <span class="btn-inner--icon"> X </span>
                                        </button>
                                    </div>
                                       
                                </div>
                                
                                
                            </div>
                        </div>
                    </div>
                </div>
                
                
            </div>

            <hr />
            
            <!-- Call to Action Buttonns -->
            <div class="form-group">
                <label for="footer"><strong><?php echo e(__('Call to Action Buttons')); ?></strong></label>
                <span class="badge badge-primary" style="color:#8898aa"><?php echo e(__('Optional')); ?></span>
                <p class="small"><?php echo e(__('Create buttons that let customers take action')); ?></p>
                <!-- Add the button -->
                <div class="text-right mt-2">
                    <button :disabled="vistiWebsite.length>1" @click="addVisitWebsite()" class="btn btn-outline-primary btn-sm" type="button">
                        <span class="btn-inner--icon"><?php echo e(__('Visit website - x2')); ?></span>
                    </button> 
                    <button v-if="!hasPhone" @click="addCallPhone()" class="btn btn-outline-primary btn-sm" type="button">
                        <span class="btn-inner--icon"><?php echo e(__('Call phone number')); ?></span>
                    </button> 
                    <button v-if="hasPhone" @click="deletePhone()" class="btn btn-outline-danger btn-sm" type="button">
                        <span class="btn-inner--icon"><?php echo e(__('Remove phone number')); ?></span>
                    </button> 
                    <button v-if="!copyOfferCode" @click="addCopyOfferCode()" class="btn btn-outline-primary btn-sm" type="button">
                        <span class="btn-inner--icon"><?php echo e(__('Copy offer code')); ?></span>
                    </button> 
                    <button v-if="copyOfferCode" @click="deleteCopyOfferCode()" class="btn btn-outline-danger btn-sm" type="button">
                        <span class="btn-inner--icon"><?php echo e(__('Remove offer code')); ?></span>
                    </button>
                </div>
            </div>

            <div class="mt-2" v-if="vistiWebsite.length>0">
                <div class="form-group p-4 "  style="background-color: #e9ecef; !important">
                    <label><strong><?php echo e(__('Visit Website buttons')); ?></strong></label>
                    <div v-for="(v, index) in vistiWebsite" class="form-group">
                        <div class="input-groups">
                            
                            <div class="row">
                                <div class="col-4">
                                    <input v-model="vistiWebsite[index]['title']" type="text" class="form-control" placeholder="<?php echo e(__('Button text')); ?>">
                                </div>
                                <div class="col-7">  
                                    <input v-model="vistiWebsite[index]['url']" type="text" class="form-control" placeholder="<?php echo e(__('URL')); ?>">
                                </div>
                                <div class="col-1 mt-2">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" @click="deleteVisitWebsite(index)">
                                        <span class="btn-inner--icon"> X </span>
                                    </button>
                                </div>
                                   
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Call phone number -->
            <div class="mt-2" v-if="hasPhone">
                <div class="form-group p-4 "  style="background-color: #e9ecef; !important">
                    <div class="form-group">
                        <label for="call_phone"><strong><?php echo e(__('Call phone number')); ?></strong></label>
                        <div class="input-group">
                            <input v-model="callPhoneButtonText" type="text" name="call_phone_name" id="call_phone_name" class="form-control" placeholder="<?php echo e(__('Button name')); ?>" value="<?php echo e(old('call_phone_name')); ?>">
                        </div>
                        <div class="input-group mt-2">
                            <input v-model="dialCode" type="text" name="call_phone_dial_code" id="call_phone_dial_code" class="form-control" placeholder="<?php echo e(__('Dial code')); ?>" value="<?php echo e(old('call_phone_dial_code')); ?>">
                            <input v-model="phoneNumber" type="text" name="call_phone_number" id="call_phone_number" class="form-control" placeholder="<?php echo e(__('Phone number')); ?>" value="<?php echo e(old('call_phone_number')); ?>">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Copy offer code -->
            <div class="mt-2" v-if="copyOfferCode">
                <div class="form-group p-4 "  style="background-color: #e9ecef; !important">
                    <div class="form-group">
                        <label for="offer_code"><strong><?php echo e(__('Offer code button')); ?></strong></label>
                        <div class="input-group">
                            <input v-model="offerCode" type="text" name="offer_code" id="offer_code" class="form-control" placeholder="<?php echo e(__('Offer code sample')); ?>" value="<?php echo e(old('offer_code')); ?>">
                        </div>
                    </div>
                </div>
            </div>



            

           
        </div>
    </div>
</div><?php /**PATH C:\xampp\htdocs\zaptra\modules\Wpbox\Providers/../Resources/views/templates/partials/form.blade.php ENDPATH**/ ?>