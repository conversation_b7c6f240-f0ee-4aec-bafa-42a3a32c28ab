
<?php $__env->startSection('thead'); ?>
    <th><?php echo e(__('Name')); ?></th>
    <th><?php echo e(__('Status')); ?></th>
    <th><?php echo e(__('Category')); ?></th>
    <th><?php echo e(__('Language')); ?></th>
    <th><?php echo e(__('Actions')); ?></th>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('tbody'); ?>
    <?php $__currentLoopData = $setup['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tr>
            <td><?php echo e($item->name); ?></td>
            <td>
                <?php if($item->status == 'APPROVED'): ?> 
                    <span class="badge badge-success"><?php echo e(__($item->status)); ?></span>
                <?php else: ?>
                    <span class="badge badge-warning"><?php echo e(__($item->status)); ?></span>
                <?php endif; ?>
            </td>
            <td><?php echo e(__($item->category)); ?></td>
            <td><?php echo e(__($item->language)); ?></td>
            <td>
                <form action="<?php echo e(route('templates.destroy', $item->id)); ?>" method="POST" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submitt" class="btn btn-outline-danger btn-sm" onclick="return confirm('<?php echo e(__('Are you sure you want to delete this template?')); ?>')"><?php echo e(__('Delete')); ?></button>
                </form>
            </td>
        </tr> 
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('general.index', $setup, \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaptra\modules\Wpbox\Providers/../Resources/views/templates/index.blade.php ENDPATH**/ ?>