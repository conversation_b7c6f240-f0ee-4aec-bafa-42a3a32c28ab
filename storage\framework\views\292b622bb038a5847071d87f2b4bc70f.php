
<?php $__env->startSection('head'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="header  pb-8 pt-5 pt-md-8">
    <div class="container-fluid">
        <div class="header-body">
            <h1 class="mb-3 mt--3"><?php echo e(__('Create new template')); ?></h1>
            <div class="row align-items-center pt-2">
            </div>
        </div>
    </div>
</div>

<form method="POST" action="<?php echo e(route('campaigns.store')); ?>" id="template_creator" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>
    <div class="container-fluid mt--7" id="tempplate_managment">
        <div class="row">
            <?php echo $__env->make('wpbox::templates.partials.basic', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php echo $__env->make('wpbox::templates.partials.form', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php echo $__env->make('wpbox::templates.partials.preview', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>
</form>
<?php echo $__env->make('wpbox::templates.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('layouts.app', ['title' => __('Send new campaign')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaptra\modules\Wpbox\Providers/../Resources/views/templates/create.blade.php ENDPATH**/ ?>